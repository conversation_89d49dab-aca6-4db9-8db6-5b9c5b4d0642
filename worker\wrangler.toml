name = "cron-task-worker"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# KV Namespaces
[[kv_namespaces]]
binding = "TASKS_KV"
id = "33866788bd034403ae17d04a79ecea00"

[[kv_namespaces]]
binding = "FAIL_LOGS_KV"
id = "57bfb32364e144f48cf558d1a302725b"

# Durable Objects
[[durable_objects.bindings]]
name = "TASK_EXECUTOR"
class_name = "TaskExecutorDO"

[[migrations]]
tag = "v1"
new_sqlite_classes = ["TaskExecutorDO"]

# Cron Triggers
# 主调度器(动态优化) | 备用调度器(安全网) | 看门狗(系统维护)
[triggers]
crons = ["*/1 * * * *", "0 */1 * * *", "0 */6 * * *"]

# Environment Variables (non-sensitive)
[vars]
ENVIRONMENT = "production"
MAX_RETRY_COUNT = "3"
DEFAULT_TIMEOUT = "30000"
LOG_RETENTION_COUNT = "50"
WORKER_NAME = "cron-task-worker"
CRON_UPDATE_MIN_INTERVAL = "300000"  # 5 minutes in milliseconds
ENABLE_DYNAMIC_CRON = "true"
CRON_STRATEGY = "exact_match"  # "exact_match" or "frequency_optimization"

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true


