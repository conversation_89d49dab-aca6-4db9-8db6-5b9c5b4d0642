{"name": "cron-task-worker", "version": "1.0.0", "description": "Cloudflare Worker backend for cron task management system", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "test": "vitest", "lint": "eslint src --ext .js", "format": "prettier --write src/**/*.js"}, "dependencies": {"hono": "^4.0.0", "jose": "^5.0.0", "cron-parser": "^4.9.0", "uuid": "^9.0.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240208.0", "wrangler": "^3.28.0", "vitest": "^1.2.0", "eslint": "^8.56.0", "prettier": "^3.2.0"}, "keywords": ["cloudflare-workers", "cron", "task-scheduler", "durable-objects"], "author": "", "license": "MIT"}