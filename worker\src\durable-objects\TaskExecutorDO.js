/**
 * TaskExecutorDO - Durable Object for executing individual tasks
 * Each task gets its own instance of this DO for state isolation and concurrency control
 */

import { TaskKV, LogKV } from '../utils/kv.js';
import { createFailureLog, createSuccessLog, createTimeoutLog, createRetryLog } from '../schemas/log.js';
import { ExecutorRegistry } from '../executors/registry.js';

export class TaskExecutorDO {
  constructor(state, env) {
    this.state = state;
    this.env = env;
    this.taskKV = new TaskKV(env.TASKS_KV);
    this.logKV = new LogKV(env.FAIL_LOGS_KV);
  }

  /**
   * Handles incoming requests to the Durable Object
   * @param {Request} request - Incoming request
   * @returns {Response} - Response
   */
  async fetch(request) {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      if (path === '/execute' && request.method === 'POST') {
        return await this.handleExecute(request);
      }

      if (path === '/status' && request.method === 'GET') {
        return await this.handleStatus();
      }

      return new Response('Not Found', { status: 404 });
    } catch (error) {
      console.error('TaskExecutorDO fetch error:', error);
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * Handles task execution requests
   * @param {Request} request - Execution request
   * @returns {Response} - Response
   */
  async handleExecute(request) {
    try {
      const body = await request.json();
      const { taskId, scheduledTime } = body;

      // Check if task is already running
      const isRunning = await this.state.storage.get('isRunning');
      if (isRunning) {
        console.log(`Task ${taskId} is already running, skipping`);
        return new Response(JSON.stringify({
          status: 'skipped',
          reason: 'already_running'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Set running flag
      await this.state.storage.put('isRunning', true);

      try {
        // Execute the task
        await this.executeTask(taskId, scheduledTime);
        
        return new Response(JSON.stringify({
          status: 'executed',
          timestamp: new Date().toISOString()
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } finally {
        // Always clear the running flag
        await this.state.storage.delete('isRunning');
      }

    } catch (error) {
      console.error('Execute handler error:', error);
      await this.state.storage.delete('isRunning');
      
      return new Response(JSON.stringify({
        status: 'error',
        message: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * Handles status requests
   * @returns {Response} - Status response
   */
  async handleStatus() {
    const isRunning = await this.state.storage.get('isRunning') || false;
    const retryCount = await this.state.storage.get('retryCount') || 0;
    const lastExecution = await this.state.storage.get('lastExecution');

    return new Response(JSON.stringify({
      isRunning,
      retryCount,
      lastExecution
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Handles alarm events (for retries)
   * @returns {Promise<void>}
   */
  async alarm() {
    try {
      console.log('TaskExecutorDO alarm triggered for retry');
      
      const retryData = await this.state.storage.get('retryData');
      if (!retryData) {
        console.log('No retry data found, ignoring alarm');
        return;
      }

      const { taskId, scheduledTime } = retryData;
      
      // Clear retry data
      await this.state.storage.delete('retryData');
      
      // Execute the task
      await this.executeTask(taskId, scheduledTime, true);
      
    } catch (error) {
      console.error('Alarm handler error:', error);
    }
  }

  /**
   * Executes a task
   * @param {string} taskId - Task ID
   * @param {string} scheduledTime - Scheduled execution time
   * @param {boolean} isRetry - Whether this is a retry attempt
   * @returns {Promise<void>}
   */
  async executeTask(taskId, scheduledTime, isRetry = false) {
    const startTime = Date.now();
    let task = null;
    let retryCount = 0;

    try {
      // Get current retry count
      retryCount = await this.state.storage.get('retryCount') || 0;
      
      // Get task configuration
      task = await this.taskKV.getTask(taskId);
      if (!task) {
        throw new Error(`Task ${taskId} not found`);
      }

      console.log(`Executing task ${task.name} (${taskId}), retry: ${retryCount}`);

      // Get executor for this task type
      const executor = ExecutorRegistry.getExecutor(task.type);
      if (!executor) {
        throw new Error(`No executor found for task type: ${task.type}`);
      }

      // Set up timeout
      const timeout = task.timeout || 30000;
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Task execution timeout')), timeout);
      });

      // Execute task with timeout
      const executionPromise = executor.execute(task.config, this.env);
      const result = await Promise.race([executionPromise, timeoutPromise]);

      // Task succeeded
      const executionTime = Date.now() - startTime;
      
      // Log success
      const successLog = createSuccessLog(taskId, task.name, result, executionTime);
      // Note: Only log failures to FAIL_LOGS_KV as per requirements
      
      // Update task last run time
      await this.taskKV.updateLastRunTime(taskId, new Date(scheduledTime));
      
      // Clear retry count on success
      await this.state.storage.delete('retryCount');
      
      // Store last execution info
      await this.state.storage.put('lastExecution', {
        timestamp: new Date().toISOString(),
        status: 'success',
        executionTime
      });

      console.log(`Task ${task.name} executed successfully in ${executionTime}ms`);

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      console.error(`Task ${taskId} execution failed:`, error);

      // Determine if this is a timeout
      const isTimeout = error.message === 'Task execution timeout';
      
      // Create failure log
      const failureLog = isTimeout 
        ? createTimeoutLog(taskId, task?.name || taskId, task?.timeout || 30000, retryCount)
        : createFailureLog(taskId, task?.name || taskId, error, {}, retryCount, executionTime);
      
      // Log failure
      await this.logKV.addLog(failureLog);

      // Store last execution info
      await this.state.storage.put('lastExecution', {
        timestamp: new Date().toISOString(),
        status: 'failure',
        error: error.message,
        executionTime
      });

      // Handle retry logic
      if (task && task.retryConfig && task.retryConfig.enabled && retryCount < task.retryConfig.maxRetries) {
        await this.scheduleRetry(taskId, task, scheduledTime, retryCount + 1, error.message);
      } else {
        // No more retries, clear retry count
        await this.state.storage.delete('retryCount');
        console.log(`Task ${taskId} failed permanently after ${retryCount} retries`);
      }
    }
  }

  /**
   * Schedules a retry for a failed task
   * @param {string} taskId - Task ID
   * @param {Object} task - Task configuration
   * @param {string} scheduledTime - Original scheduled time
   * @param {number} retryCount - Current retry count
   * @param {string} reason - Failure reason
   * @returns {Promise<void>}
   */
  async scheduleRetry(taskId, task, scheduledTime, retryCount, reason) {
    try {
      // Calculate retry delay based on strategy
      const delay = this.calculateRetryDelay(task.retryConfig, retryCount);
      
      // Update retry count
      await this.state.storage.put('retryCount', retryCount);
      
      // Store retry data for alarm handler
      await this.state.storage.put('retryData', {
        taskId,
        scheduledTime,
        retryCount,
        reason
      });

      // Schedule alarm for retry
      const retryTime = Date.now() + delay;
      await this.state.storage.setAlarm(retryTime);

      // Log retry scheduling
      const retryLog = createRetryLog(taskId, task.name, retryCount, delay, reason);
      await this.logKV.addLog(retryLog);

      console.log(`Task ${taskId} scheduled for retry ${retryCount} in ${delay}ms`);

    } catch (error) {
      console.error(`Error scheduling retry for task ${taskId}:`, error);
    }
  }

  /**
   * Calculates retry delay based on retry strategy
   * @param {Object} retryConfig - Retry configuration
   * @param {number} retryCount - Current retry count
   * @returns {number} - Delay in milliseconds
   */
  calculateRetryDelay(retryConfig, retryCount) {
    const { strategy, baseDelay = 1000, maxDelay = 60000 } = retryConfig;
    
    let delay;
    
    switch (strategy) {
      case 'fixed':
        delay = baseDelay;
        break;
        
      case 'linear':
        delay = baseDelay * retryCount;
        break;
        
      case 'exponential':
      default:
        delay = baseDelay * Math.pow(2, retryCount - 1);
        break;
    }
    
    // Apply jitter (±25% randomization)
    const jitter = delay * 0.25 * (Math.random() - 0.5);
    delay += jitter;
    
    // Ensure delay is within bounds
    return Math.min(Math.max(delay, 1000), maxDelay);
  }
}
