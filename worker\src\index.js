import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';

// Import handlers
import { handleScheduled } from './handlers/scheduled.js';
import { handleFetch } from './handlers/fetch.js';

// Import Durable Objects
import { TaskExecutorDO } from './durable-objects/TaskExecutorDO.js';

// Create Hono app
const app = new Hono();

// Middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('/api/*', cors({
  origin: (origin, c) => {
    // Allow localhost for development
    if (origin === 'http://localhost:5173') {
      return origin;
    }
    // Allow specific production domain
    if (origin === 'https://cron-task-frontend.pages.dev') {
      return origin;
    }
    // Allow any *.pages.dev domain
    if (origin && origin.match(/^https:\/\/.*\.pages\.dev$/)) {
      return origin;
    }
    // Default fallback
    return 'https://cron-task-frontend.pages.dev';
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers'
  ],
  credentials: true,
  exposeHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  maxAge: 86400, // 24 hours
}));

// Health check endpoint
app.get('/', (c) => {
  return c.json({
    message: 'Cron Task Worker is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API routes will be added here
app.route('/api', handleFetch);

// Export handlers
export default {
  /**
   * Fetch handler for HTTP requests
   */
  async fetch(request, env, ctx) {
    try {
      return await app.fetch(request, env, ctx);
    } catch (error) {
      console.error('Fetch handler error:', error);
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  },

  /**
   * Scheduled handler for cron triggers
   */
  async scheduled(event, env, ctx) {
    try {
      await handleScheduled(event, env, ctx);
    } catch (error) {
      console.error('Scheduled handler error:', error);
      // Don't throw error to prevent cron from retrying
    }
  }
};

// Export Durable Objects
export { TaskExecutorDO };
